using LoanCovenantAPI.Data;
using LoanCovenantAPI.Models;
using Microsoft.EntityFrameworkCore;
using System.Net.Mail;
using System.Net;

namespace LoanCovenantAPI.Services
{
    /// <summary>
    /// Service interface for notifications
    /// </summary>
    public interface INotificationService
    {
        Task SendComplianceAlert(LoanCovenant covenant, CovenantComplianceHistory complianceHistory);
        Task SendConcessionRevertedNotification(LoanCovenant covenant, string justification);
        Task SendPeriodicComplianceReport();
        Task ProcessPendingNotifications();
    }

    /// <summary>
    /// Service for handling notifications and alerts
    /// </summary>
    public class NotificationService : INotificationService
    {
        private readonly LoanCovenantDbContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<NotificationService> _logger;
        private readonly SmtpClient _smtpClient;

        public NotificationService(
            LoanCovenantDbContext context,
            IConfiguration configuration,
            ILogger<NotificationService> logger)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
            _smtpClient = ConfigureSmtpClient();
        }

        /// <summary>
        /// Sends compliance alert notification
        /// </summary>
        public async Task SendComplianceAlert(LoanCovenant covenant, CovenantComplianceHistory complianceHistory)
        {
            try
            {
                var subject = $"Loan Covenant Compliance Alert - {covenant.CustomerName} ({covenant.CustomerAccountNumber})";
                
                var message = $@"
Dear Stakeholder,

Please note that {covenant.CustomerName} (Account: {covenant.CustomerAccountNumber}) has defaulted on the agreed covenant as stated in the offer letter.

Covenant Details:
- Type: {covenant.CovenantType}
- Description: {covenant.CovenantDescription}
- Required Value: {complianceHistory.RequiredValue:N2}
- Actual Value: {complianceHistory.ActualValue:N2}
- Variance: {complianceHistory.VarianceAmount:N2} ({complianceHistory.VariancePercentage:N2}%)
- Check Date: {complianceHistory.CheckDate:yyyy-MM-dd HH:mm}

Kindly reach out to the customer to address this compliance issue.

Best regards,
Loan Covenant Monitoring System
";

                var recipients = await GetStakeholderEmails(covenant);
                var ccRecipients = "<EMAIL>,<EMAIL>";

                var notification = new CovenantNotification
                {
                    CovenantId = covenant.CovenantId,
                    NotificationType = NotificationType.ComplianceAlert,
                    Subject = subject,
                    Message = message,
                    Recipients = string.Join(",", recipients),
                    CcRecipients = ccRecipients,
                    ScheduledDate = DateTime.UtcNow,
                    Status = NotificationStatus.Pending,
                    CreatedBy = "System",
                    CreatedDate = DateTime.UtcNow
                };

                _context.CovenantNotifications.Add(notification);
                await _context.SaveChangesAsync();

                // Send immediately for compliance alerts
                await SendNotification(notification);

                _logger.LogInformation("Compliance alert sent for covenant {CovenantId}", covenant.CovenantId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending compliance alert for covenant {CovenantId}", covenant.CovenantId);
                throw;
            }
        }

        /// <summary>
        /// Sends notification when concession is reverted
        /// </summary>
        public async Task SendConcessionRevertedNotification(LoanCovenant covenant, string justification)
        {
            try
            {
                var subject = $"Concession Reverted - {covenant.CustomerName} ({covenant.CustomerAccountNumber})";
                
                var message = $@"
Dear Stakeholder,

This is to notify you that the concession for {covenant.CustomerName} (Account: {covenant.CustomerAccountNumber}) has been reverted.

Covenant Details:
- Type: {covenant.CovenantType}
- Description: {covenant.CovenantDescription}
- Concession Type: {covenant.ConcessionType}
- Justification: {justification}
- Reverted Date: {DateTime.UtcNow:yyyy-MM-dd HH:mm}

The customer's account has been updated accordingly.

Best regards,
Loan Covenant Monitoring System
";

                var recipients = await GetStakeholderEmails(covenant);
                var ccRecipients = "<EMAIL>,<EMAIL>";

                var notification = new CovenantNotification
                {
                    CovenantId = covenant.CovenantId,
                    NotificationType = NotificationType.ComplianceAchieved,
                    Subject = subject,
                    Message = message,
                    Recipients = string.Join(",", recipients),
                    CcRecipients = ccRecipients,
                    ScheduledDate = DateTime.UtcNow,
                    Status = NotificationStatus.Pending,
                    CreatedBy = "System",
                    CreatedDate = DateTime.UtcNow
                };

                _context.CovenantNotifications.Add(notification);
                await _context.SaveChangesAsync();

                await SendNotification(notification);

                _logger.LogInformation("Concession reverted notification sent for covenant {CovenantId}", covenant.CovenantId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending concession reverted notification for covenant {CovenantId}", covenant.CovenantId);
                throw;
            }
        }

        /// <summary>
        /// Sends periodic compliance report
        /// </summary>
        public async Task SendPeriodicComplianceReport()
        {
            try
            {
                var reportData = await GenerateComplianceReportData();
                
                var subject = $"Monthly Covenant Compliance Report - {DateTime.UtcNow:MMMM yyyy}";
                
                var message = $@"
Dear Stakeholders,

Please find below the monthly covenant compliance report:

Summary:
- Total Active Covenants: {reportData.TotalActiveCovenants}
- Compliant Covenants: {reportData.CompliantCovenants}
- Non-Compliant Covenants: {reportData.NonCompliantCovenants}
- Pending Review: {reportData.PendingReview}
- Compliance Rate: {reportData.ComplianceRate:P2}

Non-Compliant Covenants:
{string.Join("\n", reportData.NonCompliantDetails.Select(d => $"- {d.CustomerName} ({d.AccountNumber}): {d.CovenantType}"))}

For detailed reports, please access the Loan Covenant Portal or contact the Loan Review and Monitoring team.

Best regards,
Loan Covenant Monitoring System
";

                var recipients = "<EMAIL>,<EMAIL>,<EMAIL>";

                var notification = new CovenantNotification
                {
                    CovenantId = 0, // System-level notification
                    NotificationType = NotificationType.SystemAlert,
                    Subject = subject,
                    Message = message,
                    Recipients = recipients,
                    ScheduledDate = DateTime.UtcNow,
                    Status = NotificationStatus.Pending,
                    CreatedBy = "System",
                    CreatedDate = DateTime.UtcNow
                };

                _context.CovenantNotifications.Add(notification);
                await _context.SaveChangesAsync();

                await SendNotification(notification);

                _logger.LogInformation("Periodic compliance report sent");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending periodic compliance report");
                throw;
            }
        }

        /// <summary>
        /// Processes pending notifications
        /// </summary>
        public async Task ProcessPendingNotifications()
        {
            try
            {
                var pendingNotifications = await _context.CovenantNotifications
                    .Where(n => n.Status == NotificationStatus.Pending && 
                               n.ScheduledDate <= DateTime.UtcNow)
                    .ToListAsync();

                foreach (var notification in pendingNotifications)
                {
                    await SendNotification(notification);
                }

                _logger.LogInformation("Processed {Count} pending notifications", pendingNotifications.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing pending notifications");
                throw;
            }
        }

        private async Task SendNotification(CovenantNotification notification)
        {
            try
            {
                var mailMessage = new MailMessage
                {
                    From = new MailAddress(_configuration["Email:FromAddress"] ?? "<EMAIL>"),
                    Subject = notification.Subject,
                    Body = notification.Message,
                    IsBodyHtml = false
                };

                // Add recipients
                foreach (var recipient in notification.Recipients.Split(',', StringSplitOptions.RemoveEmptyEntries))
                {
                    mailMessage.To.Add(recipient.Trim());
                }

                // Add CC recipients
                if (!string.IsNullOrEmpty(notification.CcRecipients))
                {
                    foreach (var ccRecipient in notification.CcRecipients.Split(',', StringSplitOptions.RemoveEmptyEntries))
                    {
                        mailMessage.CC.Add(ccRecipient.Trim());
                    }
                }

                await _smtpClient.SendMailAsync(mailMessage);

                notification.Status = NotificationStatus.Sent;
                notification.SentDate = DateTime.UtcNow;
                
                _logger.LogInformation("Notification {NotificationId} sent successfully", notification.NotificationId);
            }
            catch (Exception ex)
            {
                notification.Status = NotificationStatus.Failed;
                notification.ErrorMessage = ex.Message;
                notification.RetryCount++;
                
                // Schedule retry if under retry limit
                if (notification.RetryCount < 3)
                {
                    notification.Status = NotificationStatus.Retrying;
                    notification.NextRetryDate = DateTime.UtcNow.AddMinutes(30 * notification.RetryCount);
                }

                _logger.LogError(ex, "Error sending notification {NotificationId}", notification.NotificationId);
            }
            finally
            {
                await _context.SaveChangesAsync();
            }
        }

        private async Task<List<string>> GetStakeholderEmails(LoanCovenant covenant)
        {
            // This would typically integrate with Active Directory or user management system
            // For now, return default stakeholder emails
            var emails = new List<string>
            {
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
            };

            // Add customer-specific BDM if available
            // var customerBdm = await GetCustomerBdmEmail(covenant.CustomerAccountNumber);
            // if (!string.IsNullOrEmpty(customerBdm))
            // {
            //     emails.Add(customerBdm);
            // }

            return emails;
        }

        private async Task<ComplianceReportData> GenerateComplianceReportData()
        {
            var activeCovenants = await _context.LoanCovenants
                .Where(c => c.Status == CovenantStatus.Active)
                .ToListAsync();

            var totalActive = activeCovenants.Count;
            var compliant = activeCovenants.Count(c => c.ComplianceStatus == ComplianceStatus.Compliant);
            var nonCompliant = activeCovenants.Count(c => c.ComplianceStatus == ComplianceStatus.NonCompliant);
            var pendingReview = activeCovenants.Count(c => c.ComplianceStatus == ComplianceStatus.PendingReview);

            var nonCompliantDetails = activeCovenants
                .Where(c => c.ComplianceStatus == ComplianceStatus.NonCompliant)
                .Select(c => new NonCompliantDetail
                {
                    CustomerName = c.CustomerName,
                    AccountNumber = c.CustomerAccountNumber,
                    CovenantType = c.CovenantType.ToString()
                })
                .ToList();

            return new ComplianceReportData
            {
                TotalActiveCovenants = totalActive,
                CompliantCovenants = compliant,
                NonCompliantCovenants = nonCompliant,
                PendingReview = pendingReview,
                ComplianceRate = totalActive > 0 ? (double)compliant / totalActive : 0,
                NonCompliantDetails = nonCompliantDetails
            };
        }

        private SmtpClient ConfigureSmtpClient()
        {
            var smtpHost = _configuration["Email:SmtpHost"] ?? "smtp.office365.com";
            var smtpPort = int.Parse(_configuration["Email:SmtpPort"] ?? "587");
            var username = _configuration["Email:Username"];
            var password = _configuration["Email:Password"];

            var client = new SmtpClient(smtpHost, smtpPort)
            {
                EnableSsl = true,
                UseDefaultCredentials = false
            };

            if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
            {
                client.Credentials = new NetworkCredential(username, password);
            }

            return client;
        }
    }

    public class ComplianceReportData
    {
        public int TotalActiveCovenants { get; set; }
        public int CompliantCovenants { get; set; }
        public int NonCompliantCovenants { get; set; }
        public int PendingReview { get; set; }
        public double ComplianceRate { get; set; }
        public List<NonCompliantDetail> NonCompliantDetails { get; set; } = new();
    }

    public class NonCompliantDetail
    {
        public string CustomerName { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string CovenantType { get; set; } = string.Empty;
    }
}
