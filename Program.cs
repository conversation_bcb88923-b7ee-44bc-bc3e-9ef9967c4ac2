using LoanCovenantAPI.Data;
using LoanCovenantAPI.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;

var builder = WebApplication.CreateBuilder(args);

// Add database context
builder.Services.AddDbContext<LoanCovenantDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection")));

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

// Configure Swagger with documentation integration
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Loan Covenant API",
        Version = "v1",
        Description = "API for managing loan covenants with integrated documentation",
        Contact = new OpenApiContact
        {
            Name = "API Support",
            Email = "<EMAIL>"
        }
    });

    // Include XML comments for API documentation
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }

    // Add documentation endpoint to Swagger
    c.DocumentFilter<DocumentationEndpointFilter>();
});

// Register application services
builder.Services.AddScoped<IDocumentationService, DocumentationService>();
builder.Services.AddScoped<ICovenantService, CovenantService>();
builder.Services.AddScoped<INotificationService, NotificationService>();
builder.Services.AddHttpClient<ICoreSystemIntegrationService, CoreSystemIntegrationService>();

// Configure logging
builder.Services.AddLogging(logging =>
{
    logging.AddConsole();
    logging.AddDebug();
});

// Configure CORS if needed
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowDocumentationAccess", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Loan Covenant API v1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
        c.DocumentTitle = "Loan Covenant API Documentation";
        c.DefaultModelsExpandDepth(-1); // Hide models section by default
    });
}

app.UseHttpsRedirection();
app.UseCors("AllowDocumentationAccess");
app.UseAuthorization();

// Serve static files from docs folder
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(
        Path.Combine(Directory.GetCurrentDirectory(), "docs")),
    RequestPath = "/docs"
});

app.MapControllers();

// Add a health check endpoint
app.MapGet("/health", () => Results.Ok(new { Status = "Healthy", Timestamp = DateTime.UtcNow }))
   .WithTags("Health")
   .WithSummary("Health check endpoint");

// Add documentation info endpoint
app.MapGet("/api/documentation/status", async (IDocumentationService docService) =>
{
    var docPath = Path.Combine(Directory.GetCurrentDirectory(), "docs", "LoanCovenantAPI-Documentation.docx");
    var isValid = await docService.ValidateDocumentationAsync(docPath);
    
    return Results.Ok(new 
    { 
        DocumentationAvailable = isValid,
        LastChecked = DateTime.UtcNow,
        DocumentPath = "/api/documentation/download"
    });
})
.WithTags("Documentation")
.WithSummary("Check documentation availability");

app.Run();

// Custom Swagger filter to add documentation endpoints
public class DocumentationEndpointFilter : IDocumentFilter
{
    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        swaggerDoc.Info.Description += "\n\n**Additional Resources:**\n" +
            "- [Download Full Documentation](/api/documentation/download)\n" +
            "- [Documentation Status](/api/documentation/status)";
    }
}
