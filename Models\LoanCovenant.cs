using System.ComponentModel.DataAnnotations;

namespace LoanCovenantAPI.Models
{
    /// <summary>
    /// Represents a loan covenant or commitment made by an obligor
    /// </summary>
    public class LoanCovenant
    {
        [Key]
        public int CovenantId { get; set; }

        [Required]
        [StringLength(50)]
        public string CustomerAccountNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string CustomerName { get; set; } = string.Empty;

        [Required]
        public CovenantType CovenantType { get; set; }

        [Required]
        public CovenantClassification Classification { get; set; }

        [Required]
        [StringLength(1000)]
        public string CovenantDescription { get; set; } = string.Empty;

        [StringLength(500)]
        public string CovenantTerms { get; set; } = string.Empty;

        public DateTime EffectiveDate { get; set; }

        public DateTime? ExpiryDate { get; set; }

        [Required]
        public ComplianceFrequency MonitoringFrequency { get; set; }

        public DateTime NextReviewDate { get; set; }

        [Required]
        public CovenantStatus Status { get; set; }

        public ComplianceStatus ComplianceStatus { get; set; }

        public DateTime LastComplianceCheck { get; set; }

        public DateTime? LastComplianceDate { get; set; }

        [StringLength(1000)]
        public string ComplianceNotes { get; set; } = string.Empty;

        // Concession-related fields
        public bool HasConcession { get; set; }

        public ConcessionType? ConcessionType { get; set; }

        [StringLength(500)]
        public string ConcessionDetails { get; set; } = string.Empty;

        public DateTime? ConcessionStartDate { get; set; }

        public DateTime? ConcessionEndDate { get; set; }

        public bool IsConcessionActive { get; set; }

        // Financial covenant specific fields
        public decimal? TargetValue { get; set; }

        public decimal? CurrentValue { get; set; }

        public decimal? ThresholdValue { get; set; }

        [StringLength(20)]
        public string? ComparisonOperator { get; set; } // >=, <=, =, etc.

        [StringLength(50)]
        public string? MeasurementUnit { get; set; }

        // Audit fields
        [Required]
        [StringLength(100)]
        public string CreatedBy { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; }

        [StringLength(100)]
        public string? ModifiedBy { get; set; }

        public DateTime? ModifiedDate { get; set; }

        [StringLength(100)]
        public string? ApprovedBy { get; set; }

        public DateTime? ApprovedDate { get; set; }

        // Navigation properties
        public virtual ICollection<CovenantComplianceHistory> ComplianceHistory { get; set; } = new List<CovenantComplianceHistory>();
        public virtual ICollection<CovenantNotification> Notifications { get; set; } = new List<CovenantNotification>();
    }

    /// <summary>
    /// Types of loan covenants
    /// </summary>
    public enum CovenantType
    {
        Financial = 1,
        NonFinancial = 2,
        Operational = 3,
        Reporting = 4,
        Maintenance = 5
    }

    /// <summary>
    /// Classification of covenants
    /// </summary>
    public enum CovenantClassification
    {
        Positive = 1,    // Things the borrower must do
        Negative = 2,    // Things the borrower must not do
        Financial = 3,   // Financial ratios and metrics
        Information = 4  // Reporting and disclosure requirements
    }

    /// <summary>
    /// Frequency of compliance monitoring
    /// </summary>
    public enum ComplianceFrequency
    {
        Daily = 1,
        Weekly = 2,
        Monthly = 3,
        Quarterly = 4,
        SemiAnnually = 5,
        Annually = 6,
        OnDemand = 7
    }

    /// <summary>
    /// Status of the covenant
    /// </summary>
    public enum CovenantStatus
    {
        Active = 1,
        Inactive = 2,
        Suspended = 3,
        Expired = 4,
        Waived = 5
    }

    /// <summary>
    /// Compliance status
    /// </summary>
    public enum ComplianceStatus
    {
        Compliant = 1,
        NonCompliant = 2,
        PendingReview = 3,
        Waived = 4,
        NotApplicable = 5
    }

    /// <summary>
    /// Types of concessions
    /// </summary>
    public enum ConcessionType
    {
        InterestRateReduction = 1,
        AMCCOTWaiver = 2,
        TurnoverConcession = 3,
        FeeWaiver = 4,
        CollateralRelease = 5,
        Other = 6
    }
}
