# PowerShell script to read Word document content
param(
    [string]$DocumentPath = "LOAN COVENANT PID.docx"
)

try {
    # Check if file exists
    if (-not (Test-Path $DocumentPath)) {
        Write-Host "File not found: $DocumentPath" -ForegroundColor Red
        exit 1
    }

    Write-Host "Reading document: $DocumentPath" -ForegroundColor Green
    Write-Host ("=" * 80) -ForegroundColor Yellow

    # Create Word Application object
    $Word = New-Object -ComObject Word.Application
    $Word.Visible = $false

    # Open document
    $Document = $Word.Documents.Open($DocumentPath)

    # Extract text content
    $Content = $Document.Content.Text

    # Display content
    Write-Host $Content

    Write-Host ("=" * 80) -ForegroundColor Yellow
    Write-Host "Document read successfully. Total characters: $($Content.Length)" -ForegroundColor Green

    # Clean up
    $Document.Close()
    $Word.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($Word) | Out-Null
}
catch {
    Write-Host "Error reading document: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.Exception.StackTrace)" -ForegroundColor Red
    
    # Clean up in case of error
    if ($Document) { $Document.Close() }
    if ($Word) { $Word.Quit() }
}
