using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LoanCovenantAPI.Models
{
    /// <summary>
    /// Tracks the compliance history of loan covenants
    /// </summary>
    public class CovenantComplianceHistory
    {
        [Key]
        public int ComplianceHistoryId { get; set; }

        [Required]
        [ForeignKey("LoanCovenant")]
        public int CovenantId { get; set; }

        [Required]
        public DateTime CheckDate { get; set; }

        [Required]
        public ComplianceStatus ComplianceStatus { get; set; }

        public decimal? ActualValue { get; set; }

        public decimal? RequiredValue { get; set; }

        public decimal? VarianceAmount { get; set; }

        public decimal? VariancePercentage { get; set; }

        [StringLength(1000)]
        public string ComplianceNotes { get; set; } = string.Empty;

        [StringLength(500)]
        public string DataSource { get; set; } = string.Empty;

        public bool IsSystemGenerated { get; set; }

        [Required]
        [StringLength(100)]
        public string CheckedBy { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; }

        [StringLength(100)]
        public string? ReviewedBy { get; set; }

        public DateTime? ReviewedDate { get; set; }

        [StringLength(500)]
        public string? ReviewComments { get; set; }

        // Navigation property
        public virtual LoanCovenant LoanCovenant { get; set; } = null!;
    }

    /// <summary>
    /// Represents notifications sent for covenant compliance
    /// </summary>
    public class CovenantNotification
    {
        [Key]
        public int NotificationId { get; set; }

        [Required]
        [ForeignKey("LoanCovenant")]
        public int CovenantId { get; set; }

        [Required]
        public NotificationType NotificationType { get; set; }

        [Required]
        [StringLength(200)]
        public string Subject { get; set; } = string.Empty;

        [Required]
        [StringLength(2000)]
        public string Message { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string Recipients { get; set; } = string.Empty; // Comma-separated email addresses

        [StringLength(500)]
        public string CcRecipients { get; set; } = string.Empty;

        public DateTime ScheduledDate { get; set; }

        public DateTime? SentDate { get; set; }

        [Required]
        public NotificationStatus Status { get; set; }

        [StringLength(1000)]
        public string? ErrorMessage { get; set; }

        public int RetryCount { get; set; }

        public DateTime? NextRetryDate { get; set; }

        [Required]
        [StringLength(100)]
        public string CreatedBy { get; set; } = string.Empty;

        public DateTime CreatedDate { get; set; }

        // Navigation property
        public virtual LoanCovenant LoanCovenant { get; set; } = null!;
    }

    /// <summary>
    /// Types of notifications
    /// </summary>
    public enum NotificationType
    {
        ComplianceAlert = 1,
        ComplianceReminder = 2,
        CovenantExpiry = 3,
        ConcessionExpiry = 4,
        ComplianceAchieved = 5,
        SystemAlert = 6
    }

    /// <summary>
    /// Status of notifications
    /// </summary>
    public enum NotificationStatus
    {
        Pending = 1,
        Sent = 2,
        Failed = 3,
        Cancelled = 4,
        Retrying = 5
    }

    /// <summary>
    /// Represents user roles and permissions
    /// </summary>
    public class UserRole
    {
        [Key]
        public int RoleId { get; set; }

        [Required]
        [StringLength(100)]
        public string RoleName { get; set; } = string.Empty;

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        public bool CanCreateCovenants { get; set; }

        public bool CanModifyCovenants { get; set; }

        public bool CanDeleteCovenants { get; set; }

        public bool CanViewReports { get; set; }

        public bool CanManageConcessions { get; set; }

        public bool CanApproveCovenants { get; set; }

        public bool CanRevertInterestRates { get; set; }

        public bool IsActive { get; set; }

        public DateTime CreatedDate { get; set; }

        [StringLength(100)]
        public string CreatedBy { get; set; } = string.Empty;

        // Navigation properties
        public virtual ICollection<UserRoleAssignment> UserAssignments { get; set; } = new List<UserRoleAssignment>();
    }

    /// <summary>
    /// Maps users to their roles
    /// </summary>
    public class UserRoleAssignment
    {
        [Key]
        public int AssignmentId { get; set; }

        [Required]
        [StringLength(100)]
        public string UserId { get; set; } = string.Empty; // From Active Directory

        [Required]
        [StringLength(200)]
        public string UserName { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Email { get; set; } = string.Empty;

        [Required]
        [ForeignKey("UserRole")]
        public int RoleId { get; set; }

        public DateTime AssignedDate { get; set; }

        public DateTime? ExpiryDate { get; set; }

        public bool IsActive { get; set; }

        [Required]
        [StringLength(100)]
        public string AssignedBy { get; set; } = string.Empty;

        // Navigation property
        public virtual UserRole UserRole { get; set; } = null!;
    }
}
