using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using System.Text;

Console.WriteLine("Reading document content...\n");
Console.WriteLine("=" + new string('=', 80));

try
{
    var filePath = "LOAN COVENANT PID.docx";

    if (!File.Exists(filePath))
    {
        Console.WriteLine($"File not found: {filePath}");
        return;
    }

    using var document = WordprocessingDocument.Open(filePath, false);
    var body = document.MainDocumentPart?.Document?.Body;

    if (body == null)
    {
        Console.WriteLine("Document body is empty or invalid.");
        return;
    }

    var content = new StringBuilder();
    foreach (var paragraph in body.Elements<Paragraph>())
    {
        var text = paragraph.InnerText.Trim();
        if (!string.IsNullOrEmpty(text))
        {
            content.AppendLine(text);
            Console.WriteLine(text);
        }
    }

    Console.WriteLine("=" + new string('=', 80));
    Console.WriteLine($"\nDocument read successfully. Total characters: {content.Length}");
}
catch (Exception ex)
{
    Console.WriteLine($"Error reading document: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
}
