using LoanCovenantAPI.Services;
using System.Text.Json;

namespace LoanCovenantAPI.Middleware
{
    public class DocumentationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<DocumentationMiddleware> _logger;
        private readonly IServiceProvider _serviceProvider;

        public DocumentationMiddleware(
            RequestDelegate next, 
            ILogger<DocumentationMiddleware> logger,
            IServiceProvider serviceProvider)
        {
            _next = next;
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Check if this is a documentation-related request
            if (context.Request.Path.StartsWithSegments("/docs"))
            {
                await HandleDocumentationRequest(context);
                return;
            }

            // Add documentation headers to API responses
            if (context.Request.Path.StartsWithSegments("/api"))
            {
                context.Response.Headers["X-Documentation-Available"] = "true";
                context.Response.Headers["X-Documentation-Url"] = "/api/documentation/download";
            }

            await _next(context);
        }

        private async Task HandleDocumentationRequest(HttpContext context)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var documentationService = scope.ServiceProvider.GetRequiredService<IDocumentationService>();

                var path = context.Request.Path.Value?.ToLowerInvariant();

                switch (path)
                {
                    case "/docs":
                    case "/docs/":
                        await ServeDocumentationIndex(context);
                        break;

                    case "/docs/api":
                        await ServeApiDocumentation(context, documentationService);
                        break;

                    case "/docs/health":
                        await ServeDocumentationHealth(context, documentationService);
                        break;

                    default:
                        context.Response.StatusCode = 404;
                        await context.Response.WriteAsync("Documentation resource not found.");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling documentation request: {Path}", context.Request.Path);
                context.Response.StatusCode = 500;
                await context.Response.WriteAsync("An error occurred while processing the documentation request.");
            }
        }

        private async Task ServeDocumentationIndex(HttpContext context)
        {
            context.Response.ContentType = "text/html";
            
            var html = @"
<!DOCTYPE html>
<html>
<head>
    <title>Loan Covenant API Documentation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 5px; }
        .nav { margin: 20px 0; }
        .nav a { margin-right: 20px; text-decoration: none; color: #007bff; }
        .nav a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class='header'>
        <h1>Loan Covenant API Documentation</h1>
        <p>Welcome to the comprehensive documentation for the Loan Covenant API.</p>
    </div>
    
    <div class='nav'>
        <h3>Available Resources:</h3>
        <a href='/api/documentation/download'>Download Full Documentation (Word)</a>
        <a href='/swagger'>Interactive API Documentation (Swagger)</a>
        <a href='/docs/api'>API Documentation Summary</a>
        <a href='/docs/health'>Documentation Health Check</a>
    </div>
    
    <div>
        <h3>Quick Links:</h3>
        <ul>
            <li><a href='/api/documentation/info'>Documentation Metadata</a></li>
            <li><a href='/api/documentation/status'>Documentation Status</a></li>
            <li><a href='/health'>API Health Check</a></li>
        </ul>
    </div>
</body>
</html>";

            await context.Response.WriteAsync(html);
        }

        private async Task ServeApiDocumentation(HttpContext context, IDocumentationService documentationService)
        {
            var docPath = Path.Combine(Directory.GetCurrentDirectory(), "docs", "LoanCovenantAPI-Documentation.docx");
            
            if (!File.Exists(docPath))
            {
                context.Response.StatusCode = 404;
                await context.Response.WriteAsync("Documentation file not found.");
                return;
            }

            try
            {
                var content = await documentationService.ParseDocumentationAsync(docPath);
                context.Response.ContentType = "application/json";
                
                var jsonOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = true
                };

                var json = JsonSerializer.Serialize(content, jsonOptions);
                await context.Response.WriteAsync(json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing documentation");
                context.Response.StatusCode = 500;
                await context.Response.WriteAsync("Error parsing documentation.");
            }
        }

        private async Task ServeDocumentationHealth(HttpContext context, IDocumentationService documentationService)
        {
            var docPath = Path.Combine(Directory.GetCurrentDirectory(), "docs", "LoanCovenantAPI-Documentation.docx");
            var isValid = await documentationService.ValidateDocumentationAsync(docPath);
            
            var health = new
            {
                Status = isValid ? "Healthy" : "Unhealthy",
                DocumentationAvailable = isValid,
                LastChecked = DateTime.UtcNow,
                FilePath = docPath,
                FileExists = File.Exists(docPath),
                FileSize = File.Exists(docPath) ? new FileInfo(docPath).Length : 0,
                LastModified = File.Exists(docPath) ? File.GetLastWriteTime(docPath) : (DateTime?)null
            };

            context.Response.ContentType = "application/json";
            var json = JsonSerializer.Serialize(health, new JsonSerializerOptions 
            { 
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true 
            });
            
            await context.Response.WriteAsync(json);
        }
    }

    // Extension method to register the middleware
    public static class DocumentationMiddlewareExtensions
    {
        public static IApplicationBuilder UseDocumentationMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<DocumentationMiddleware>();
        }
    }
}
