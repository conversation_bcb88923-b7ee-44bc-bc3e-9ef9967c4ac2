using Microsoft.EntityFrameworkCore;
using LoanCovenantAPI.Models;

namespace LoanCovenantAPI.Data
{
    /// <summary>
    /// Database context for the Loan Covenant API
    /// </summary>
    public class LoanCovenantDbContext : DbContext
    {
        public LoanCovenantDbContext(DbContextOptions<LoanCovenantDbContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<LoanCovenant> LoanCovenants { get; set; }
        public DbSet<CovenantComplianceHistory> CovenantComplianceHistories { get; set; }
        public DbSet<CovenantNotification> CovenantNotifications { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<UserRoleAssignment> UserRoleAssignments { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure LoanCovenant entity
            modelBuilder.Entity<LoanCovenant>(entity =>
            {
                entity.HasKey(e => e.CovenantId);
                entity.Property(e => e.CustomerAccountNumber).IsRequired().HasMaxLength(50);
                entity.Property(e => e.CustomerName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.CovenantDescription).IsRequired().HasMaxLength(1000);
                entity.Property(e => e.CovenantTerms).HasMaxLength(500);
                entity.Property(e => e.ComplianceNotes).HasMaxLength(1000);
                entity.Property(e => e.ConcessionDetails).HasMaxLength(500);
                entity.Property(e => e.ComparisonOperator).HasMaxLength(20);
                entity.Property(e => e.MeasurementUnit).HasMaxLength(50);
                entity.Property(e => e.CreatedBy).IsRequired().HasMaxLength(100);
                entity.Property(e => e.ModifiedBy).HasMaxLength(100);
                entity.Property(e => e.ApprovedBy).HasMaxLength(100);

                // Configure decimal precision
                entity.Property(e => e.TargetValue).HasPrecision(18, 4);
                entity.Property(e => e.CurrentValue).HasPrecision(18, 4);
                entity.Property(e => e.ThresholdValue).HasPrecision(18, 4);

                // Configure indexes
                entity.HasIndex(e => e.CustomerAccountNumber).HasDatabaseName("IX_LoanCovenant_CustomerAccountNumber");
                entity.HasIndex(e => e.Status).HasDatabaseName("IX_LoanCovenant_Status");
                entity.HasIndex(e => e.ComplianceStatus).HasDatabaseName("IX_LoanCovenant_ComplianceStatus");
                entity.HasIndex(e => e.NextReviewDate).HasDatabaseName("IX_LoanCovenant_NextReviewDate");
            });

            // Configure CovenantComplianceHistory entity
            modelBuilder.Entity<CovenantComplianceHistory>(entity =>
            {
                entity.HasKey(e => e.ComplianceHistoryId);
                entity.Property(e => e.ComplianceNotes).HasMaxLength(1000);
                entity.Property(e => e.DataSource).HasMaxLength(500);
                entity.Property(e => e.CheckedBy).IsRequired().HasMaxLength(100);
                entity.Property(e => e.ReviewedBy).HasMaxLength(100);
                entity.Property(e => e.ReviewComments).HasMaxLength(500);

                // Configure decimal precision
                entity.Property(e => e.ActualValue).HasPrecision(18, 4);
                entity.Property(e => e.RequiredValue).HasPrecision(18, 4);
                entity.Property(e => e.VarianceAmount).HasPrecision(18, 4);
                entity.Property(e => e.VariancePercentage).HasPrecision(18, 4);

                // Configure relationship
                entity.HasOne(e => e.LoanCovenant)
                      .WithMany(e => e.ComplianceHistory)
                      .HasForeignKey(e => e.CovenantId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure indexes
                entity.HasIndex(e => e.CovenantId).HasDatabaseName("IX_CovenantComplianceHistory_CovenantId");
                entity.HasIndex(e => e.CheckDate).HasDatabaseName("IX_CovenantComplianceHistory_CheckDate");
                entity.HasIndex(e => e.ComplianceStatus).HasDatabaseName("IX_CovenantComplianceHistory_ComplianceStatus");
            });

            // Configure CovenantNotification entity
            modelBuilder.Entity<CovenantNotification>(entity =>
            {
                entity.HasKey(e => e.NotificationId);
                entity.Property(e => e.Subject).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Message).IsRequired().HasMaxLength(2000);
                entity.Property(e => e.Recipients).IsRequired().HasMaxLength(500);
                entity.Property(e => e.CcRecipients).HasMaxLength(500);
                entity.Property(e => e.ErrorMessage).HasMaxLength(1000);
                entity.Property(e => e.CreatedBy).IsRequired().HasMaxLength(100);

                // Configure relationship
                entity.HasOne(e => e.LoanCovenant)
                      .WithMany(e => e.Notifications)
                      .HasForeignKey(e => e.CovenantId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure indexes
                entity.HasIndex(e => e.CovenantId).HasDatabaseName("IX_CovenantNotification_CovenantId");
                entity.HasIndex(e => e.Status).HasDatabaseName("IX_CovenantNotification_Status");
                entity.HasIndex(e => e.ScheduledDate).HasDatabaseName("IX_CovenantNotification_ScheduledDate");
            });

            // Configure UserRole entity
            modelBuilder.Entity<UserRole>(entity =>
            {
                entity.HasKey(e => e.RoleId);
                entity.Property(e => e.RoleName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.CreatedBy).IsRequired().HasMaxLength(100);

                // Configure unique constraint
                entity.HasIndex(e => e.RoleName).IsUnique().HasDatabaseName("IX_UserRole_RoleName_Unique");
            });

            // Configure UserRoleAssignment entity
            modelBuilder.Entity<UserRoleAssignment>(entity =>
            {
                entity.HasKey(e => e.AssignmentId);
                entity.Property(e => e.UserId).IsRequired().HasMaxLength(100);
                entity.Property(e => e.UserName).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(200);
                entity.Property(e => e.AssignedBy).IsRequired().HasMaxLength(100);

                // Configure relationship
                entity.HasOne(e => e.UserRole)
                      .WithMany(e => e.UserAssignments)
                      .HasForeignKey(e => e.RoleId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure indexes
                entity.HasIndex(e => e.UserId).HasDatabaseName("IX_UserRoleAssignment_UserId");
                entity.HasIndex(e => e.RoleId).HasDatabaseName("IX_UserRoleAssignment_RoleId");
                entity.HasIndex(e => new { e.UserId, e.RoleId }).IsUnique().HasDatabaseName("IX_UserRoleAssignment_UserId_RoleId_Unique");
            });

            // Seed default data
            SeedDefaultData(modelBuilder);
        }

        private static void SeedDefaultData(ModelBuilder modelBuilder)
        {
            // Seed default user roles
            modelBuilder.Entity<UserRole>().HasData(
                new UserRole
                {
                    RoleId = 1,
                    RoleName = "Credit Admin Officer",
                    Description = "Can capture loan covenants and revert interest rates",
                    CanCreateCovenants = true,
                    CanModifyCovenants = true,
                    CanDeleteCovenants = false,
                    CanViewReports = true,
                    CanManageConcessions = true,
                    CanApproveCovenants = false,
                    CanRevertInterestRates = true,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = "System"
                },
                new UserRole
                {
                    RoleId = 2,
                    RoleName = "Loan Review and Monitoring Officer",
                    Description = "Can download reports and monitor compliance",
                    CanCreateCovenants = false,
                    CanModifyCovenants = false,
                    CanDeleteCovenants = false,
                    CanViewReports = true,
                    CanManageConcessions = false,
                    CanApproveCovenants = false,
                    CanRevertInterestRates = false,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = "System"
                },
                new UserRole
                {
                    RoleId = 3,
                    RoleName = "Business Development Manager",
                    Description = "Can view reports and receive notifications",
                    CanCreateCovenants = false,
                    CanModifyCovenants = false,
                    CanDeleteCovenants = false,
                    CanViewReports = true,
                    CanManageConcessions = false,
                    CanApproveCovenants = false,
                    CanRevertInterestRates = false,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = "System"
                },
                new UserRole
                {
                    RoleId = 4,
                    RoleName = "Credit Risk Management",
                    Description = "Can approve covenants and view all reports",
                    CanCreateCovenants = false,
                    CanModifyCovenants = false,
                    CanDeleteCovenants = false,
                    CanViewReports = true,
                    CanManageConcessions = false,
                    CanApproveCovenants = true,
                    CanRevertInterestRates = false,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = "System"
                },
                new UserRole
                {
                    RoleId = 5,
                    RoleName = "System Administrator",
                    Description = "Full system access and administration",
                    CanCreateCovenants = true,
                    CanModifyCovenants = true,
                    CanDeleteCovenants = true,
                    CanViewReports = true,
                    CanManageConcessions = true,
                    CanApproveCovenants = true,
                    CanRevertInterestRates = true,
                    IsActive = true,
                    CreatedDate = DateTime.UtcNow,
                    CreatedBy = "System"
                }
            );
        }
    }
}
