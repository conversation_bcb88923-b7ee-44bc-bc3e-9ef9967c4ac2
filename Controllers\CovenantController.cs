using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using LoanCovenantAPI.Data;
using LoanCovenantAPI.Models;
using LoanCovenantAPI.Services;
using System.Security.Claims;

namespace LoanCovenantAPI.Controllers
{
    /// <summary>
    /// Controller for managing loan covenants
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class CovenantController : ControllerBase
    {
        private readonly LoanCovenantDbContext _context;
        private readonly ICovenantService _covenantService;
        private readonly INotificationService _notificationService;
        private readonly ILogger<CovenantController> _logger;

        public CovenantController(
            LoanCovenantDbContext context,
            ICovenantService covenantService,
            INotificationService notificationService,
            ILogger<CovenantController> logger)
        {
            _context = context;
            _covenantService = covenantService;
            _notificationService = notificationService;
            _logger = logger;
        }

        /// <summary>
        /// Gets all loan covenants with optional filtering
        /// </summary>
        /// <param name="customerAccountNumber">Filter by customer account number</param>
        /// <param name="status">Filter by covenant status</param>
        /// <param name="complianceStatus">Filter by compliance status</param>
        /// <param name="pageNumber">Page number for pagination</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>List of loan covenants</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PagedResult<LoanCovenant>), 200)]
        public async Task<ActionResult<PagedResult<LoanCovenant>>> GetCovenants(
            [FromQuery] string? customerAccountNumber = null,
            [FromQuery] CovenantStatus? status = null,
            [FromQuery] ComplianceStatus? complianceStatus = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            try
            {
                var query = _context.LoanCovenants
                    .Include(c => c.ComplianceHistory.OrderByDescending(h => h.CheckDate).Take(5))
                    .AsQueryable();

                // Apply filters
                if (!string.IsNullOrEmpty(customerAccountNumber))
                {
                    query = query.Where(c => c.CustomerAccountNumber.Contains(customerAccountNumber));
                }

                if (status.HasValue)
                {
                    query = query.Where(c => c.Status == status.Value);
                }

                if (complianceStatus.HasValue)
                {
                    query = query.Where(c => c.ComplianceStatus == complianceStatus.Value);
                }

                var totalCount = await query.CountAsync();
                var covenants = await query
                    .OrderByDescending(c => c.CreatedDate)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var result = new PagedResult<LoanCovenant>
                {
                    Items = covenants,
                    TotalCount = totalCount,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving covenants");
                return StatusCode(500, "An error occurred while retrieving covenants.");
            }
        }

        /// <summary>
        /// Gets a specific loan covenant by ID
        /// </summary>
        /// <param name="id">Covenant ID</param>
        /// <returns>Loan covenant details</returns>
        [HttpGet("{id}")]
        [ProducesResponseType(typeof(LoanCovenant), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<LoanCovenant>> GetCovenant(int id)
        {
            try
            {
                var covenant = await _context.LoanCovenants
                    .Include(c => c.ComplianceHistory.OrderByDescending(h => h.CheckDate))
                    .Include(c => c.Notifications.OrderByDescending(n => n.CreatedDate))
                    .FirstOrDefaultAsync(c => c.CovenantId == id);

                if (covenant == null)
                {
                    return NotFound($"Covenant with ID {id} not found.");
                }

                return Ok(covenant);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving covenant {CovenantId}", id);
                return StatusCode(500, "An error occurred while retrieving the covenant.");
            }
        }

        /// <summary>
        /// Creates a new loan covenant
        /// </summary>
        /// <param name="covenant">Covenant details</param>
        /// <returns>Created covenant</returns>
        [HttpPost]
        [ProducesResponseType(typeof(LoanCovenant), 201)]
        [ProducesResponseType(400)]
        public async Task<ActionResult<LoanCovenant>> CreateCovenant([FromBody] CreateCovenantRequest covenant)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var currentUser = GetCurrentUser();
                
                // Check if user has permission to create covenants
                if (!await _covenantService.CanUserCreateCovenants(currentUser))
                {
                    return Forbid("You do not have permission to create covenants.");
                }

                var newCovenant = new LoanCovenant
                {
                    CustomerAccountNumber = covenant.CustomerAccountNumber,
                    CustomerName = covenant.CustomerName,
                    CovenantType = covenant.CovenantType,
                    Classification = covenant.Classification,
                    CovenantDescription = covenant.CovenantDescription,
                    CovenantTerms = covenant.CovenantTerms ?? string.Empty,
                    EffectiveDate = covenant.EffectiveDate,
                    ExpiryDate = covenant.ExpiryDate,
                    MonitoringFrequency = covenant.MonitoringFrequency,
                    NextReviewDate = covenant.NextReviewDate,
                    Status = CovenantStatus.Active,
                    ComplianceStatus = ComplianceStatus.PendingReview,
                    HasConcession = covenant.HasConcession,
                    ConcessionType = covenant.ConcessionType,
                    ConcessionDetails = covenant.ConcessionDetails ?? string.Empty,
                    ConcessionStartDate = covenant.ConcessionStartDate,
                    ConcessionEndDate = covenant.ConcessionEndDate,
                    IsConcessionActive = covenant.HasConcession && covenant.ConcessionStartDate <= DateTime.UtcNow && 
                                       (covenant.ConcessionEndDate == null || covenant.ConcessionEndDate > DateTime.UtcNow),
                    TargetValue = covenant.TargetValue,
                    ThresholdValue = covenant.ThresholdValue,
                    ComparisonOperator = covenant.ComparisonOperator,
                    MeasurementUnit = covenant.MeasurementUnit,
                    CreatedBy = currentUser,
                    CreatedDate = DateTime.UtcNow
                };

                _context.LoanCovenants.Add(newCovenant);
                await _context.SaveChangesAsync();

                // Schedule initial compliance check
                await _covenantService.ScheduleComplianceCheck(newCovenant.CovenantId);

                _logger.LogInformation("Covenant {CovenantId} created by {User}", newCovenant.CovenantId, currentUser);

                return CreatedAtAction(nameof(GetCovenant), new { id = newCovenant.CovenantId }, newCovenant);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating covenant");
                return StatusCode(500, "An error occurred while creating the covenant.");
            }
        }

        /// <summary>
        /// Updates an existing loan covenant
        /// </summary>
        /// <param name="id">Covenant ID</param>
        /// <param name="covenant">Updated covenant details</param>
        /// <returns>Updated covenant</returns>
        [HttpPut("{id}")]
        [ProducesResponseType(typeof(LoanCovenant), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<LoanCovenant>> UpdateCovenant(int id, [FromBody] UpdateCovenantRequest covenant)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var currentUser = GetCurrentUser();
                
                // Check if user has permission to modify covenants
                if (!await _covenantService.CanUserModifyCovenants(currentUser))
                {
                    return Forbid("You do not have permission to modify covenants.");
                }

                var existingCovenant = await _context.LoanCovenants.FindAsync(id);
                if (existingCovenant == null)
                {
                    return NotFound($"Covenant with ID {id} not found.");
                }

                // Update properties
                existingCovenant.CovenantDescription = covenant.CovenantDescription;
                existingCovenant.CovenantTerms = covenant.CovenantTerms ?? string.Empty;
                existingCovenant.ExpiryDate = covenant.ExpiryDate;
                existingCovenant.MonitoringFrequency = covenant.MonitoringFrequency;
                existingCovenant.NextReviewDate = covenant.NextReviewDate;
                existingCovenant.TargetValue = covenant.TargetValue;
                existingCovenant.ThresholdValue = covenant.ThresholdValue;
                existingCovenant.ComparisonOperator = covenant.ComparisonOperator;
                existingCovenant.MeasurementUnit = covenant.MeasurementUnit;
                existingCovenant.ModifiedBy = currentUser;
                existingCovenant.ModifiedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation("Covenant {CovenantId} updated by {User}", id, currentUser);

                return Ok(existingCovenant);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating covenant {CovenantId}", id);
                return StatusCode(500, "An error occurred while updating the covenant.");
            }
        }

        /// <summary>
        /// Performs a compliance check for a specific covenant
        /// </summary>
        /// <param name="id">Covenant ID</param>
        /// <returns>Compliance check result</returns>
        [HttpPost("{id}/compliance-check")]
        [ProducesResponseType(typeof(CovenantComplianceHistory), 200)]
        [ProducesResponseType(404)]
        public async Task<ActionResult<CovenantComplianceHistory>> PerformComplianceCheck(int id)
        {
            try
            {
                var currentUser = GetCurrentUser();
                var result = await _covenantService.PerformComplianceCheck(id, currentUser);

                if (result == null)
                {
                    return NotFound($"Covenant with ID {id} not found.");
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing compliance check for covenant {CovenantId}", id);
                return StatusCode(500, "An error occurred while performing the compliance check.");
            }
        }

        private string GetCurrentUser()
        {
            return User.Identity?.Name ?? User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "Unknown";
        }
    }

    /// <summary>
    /// Request model for creating a new covenant
    /// </summary>
    public class CreateCovenantRequest
    {
        public string CustomerAccountNumber { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public CovenantType CovenantType { get; set; }
        public CovenantClassification Classification { get; set; }
        public string CovenantDescription { get; set; } = string.Empty;
        public string? CovenantTerms { get; set; }
        public DateTime EffectiveDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public ComplianceFrequency MonitoringFrequency { get; set; }
        public DateTime NextReviewDate { get; set; }
        public bool HasConcession { get; set; }
        public ConcessionType? ConcessionType { get; set; }
        public string? ConcessionDetails { get; set; }
        public DateTime? ConcessionStartDate { get; set; }
        public DateTime? ConcessionEndDate { get; set; }
        public decimal? TargetValue { get; set; }
        public decimal? ThresholdValue { get; set; }
        public string? ComparisonOperator { get; set; }
        public string? MeasurementUnit { get; set; }
    }

    /// <summary>
    /// Request model for updating a covenant
    /// </summary>
    public class UpdateCovenantRequest
    {
        public string CovenantDescription { get; set; } = string.Empty;
        public string? CovenantTerms { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public ComplianceFrequency MonitoringFrequency { get; set; }
        public DateTime NextReviewDate { get; set; }
        public decimal? TargetValue { get; set; }
        public decimal? ThresholdValue { get; set; }
        public string? ComparisonOperator { get; set; }
        public string? MeasurementUnit { get; set; }
    }

    /// <summary>
    /// Paged result wrapper
    /// </summary>
    public class PagedResult<T>
    {
        public List<T> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }
}
