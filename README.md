# Loan Covenant API - Professional Implementation Guide

This is a comprehensive Loan Covenant Monitoring and Tracking Portal built with C# .NET 8, designed to capture, monitor, and track loan covenants/commitments made by obligors as specified in your Project Initiation Document (PID).

## 🎯 Project Overview

Based on your PID, this system provides:

- **Covenant Capture & Management**: Create and manage financial and non-financial loan covenants
- **Automated Compliance Monitoring**: Real-time monitoring with automated triggers and alerts
- **Stakeholder Notifications**: Email notifications to BDMs, Risk Management, and other stakeholders
- **Concession Management**: Handle AMC/COT waivers, turnover concessions, and interest rate reversions
- **Reporting & Analytics**: Comprehensive reporting with integration to Wema Analytics
- **Role-Based Access Control**: Secure access based on user roles and permissions
- **Active Directory Integration**: Seamless integration with corporate authentication

## 🚀 Quick Start

### 1. Prerequisites
- .NET 8 SDK
- SQL Server (LocalDB for development)
- Visual Studio 2022 or VS Code
- Access to corporate email system (for notifications)

### 2. Database Setup
```bash
# Install Entity Framework tools
dotnet tool install --global dotnet-ef

# Create and apply database migrations
dotnet ef migrations add InitialCreate
dotnet ef database update
```

### 3. Configuration
Update `appsettings.json` with your environment-specific settings:
- Database connection string
- Email server configuration
- Core banking system API endpoints
- Active Directory settings

### 4. Run the Application
```bash
dotnet restore
dotnet build
dotnet run
```

Access the application at `https://localhost:5001` or `http://localhost:5000`

## 📋 Features

### Professional Documentation Integration Options

#### Option 1: API Endpoints for Documentation
- **Download**: `GET /api/documentation/download` - Download the Word document
- **Info**: `GET /api/documentation/info` - Get document metadata
- **Status**: `GET /api/documentation/status` - Check document availability

#### Option 2: Documentation Middleware
- **Index**: `GET /docs` - Documentation portal homepage
- **API Docs**: `GET /docs/api` - Parsed documentation as JSON
- **Health**: `GET /docs/health` - Documentation health check

#### Option 3: Swagger Integration
- Interactive API documentation at `/swagger`
- Includes links to download full documentation
- XML comments integration for detailed API descriptions

## 🏗️ Architecture

### Services
- **DocumentationService**: Handles Word document parsing and validation
- **DocumentationController**: REST endpoints for documentation access
- **DocumentationMiddleware**: Custom middleware for documentation routing

### Key Components

1. **Word Document Processing**
   - Uses DocumentFormat.OpenXml for reading .docx files
   - Extracts text and structured content
   - Validates document integrity

2. **API Integration**
   - RESTful endpoints for documentation access
   - Proper error handling and logging
   - Content type detection and file serving

3. **Swagger Enhancement**
   - Custom document filters
   - XML documentation comments
   - Enhanced API descriptions

## 📁 Project Structure

```
LoanCovenantAPI/
├── Controllers/
│   └── DocumentationController.cs
├── Services/
│   └── DocumentationService.cs
├── Middleware/
│   └── DocumentationMiddleware.cs
├── docs/
│   └── LoanCovenantAPI-Documentation.docx
├── Program.cs
├── LoanCovenantAPI.csproj
└── README.md
```

## 🔧 Configuration

### Program.cs Setup
The application is configured to:
- Serve static files from the `docs` folder
- Include XML documentation in Swagger
- Register documentation services
- Enable CORS for documentation access

### Middleware Registration
Add to your Program.cs:
```csharp
app.UseDocumentationMiddleware();
```

## 📖 Usage Examples

### Download Documentation
```bash
curl -O http://localhost:5000/api/documentation/download
```

### Get Documentation Info
```bash
curl http://localhost:5000/api/documentation/info
```

### Check Documentation Status
```bash
curl http://localhost:5000/api/documentation/status
```

### Access Documentation Portal
Navigate to: `http://localhost:5000/docs`

## 🛠️ Best Practices Implemented

1. **Separation of Concerns**: Services handle business logic, controllers handle HTTP concerns
2. **Error Handling**: Comprehensive exception handling with proper HTTP status codes
3. **Logging**: Structured logging throughout the application
4. **Validation**: Document validation before processing
5. **Security**: Safe file handling and path validation
6. **Performance**: Async/await patterns for I/O operations
7. **Documentation**: XML comments for all public APIs

## 🔒 Security Considerations

- File path validation to prevent directory traversal
- Content type validation for served files
- Proper error handling without exposing internal details
- CORS configuration for controlled access

## 📊 Monitoring

The application includes:
- Health check endpoints
- Documentation availability monitoring
- Structured logging for troubleshooting
- File system monitoring capabilities

## 🚀 Deployment Notes

1. Ensure the `docs` folder is included in your deployment package
2. Configure appropriate file permissions for the docs directory
3. Set up monitoring for documentation file availability
4. Consider implementing caching for frequently accessed documents

## 🔄 Maintenance

- Regularly validate documentation files
- Monitor file sizes and access patterns
- Update documentation versions as needed
- Review logs for access patterns and errors

## 📞 Support

For issues or questions about the documentation integration:
1. Check the health endpoints for system status
2. Review application logs for detailed error information
3. Validate that documentation files are properly placed and accessible
