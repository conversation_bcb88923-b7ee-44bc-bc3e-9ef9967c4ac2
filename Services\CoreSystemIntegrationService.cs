using LoanCovenantAPI.Services;
using System.Text.Json;

namespace LoanCovenantAPI.Services
{
    /// <summary>
    /// Service interface for core banking system integration
    /// </summary>
    public interface ICoreSystemIntegrationService
    {
        Task<CustomerFinancialData?> GetCustomerFinancialData(string accountNumber);
        Task<bool> RevertInterestRate(string accountNumber, string justification, string userId);
        Task<CustomerAccountInfo?> GetCustomerAccountInfo(string accountNumber);
        Task<bool> ValidateCustomerAccount(string accountNumber);
    }

    /// <summary>
    /// Service for integrating with core banking systems
    /// </summary>
    public class CoreSystemIntegrationService : ICoreSystemIntegrationService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<CoreSystemIntegrationService> _logger;

        public CoreSystemIntegrationService(
            HttpClient httpClient,
            IConfiguration configuration,
            ILogger<CoreSystemIntegrationService> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;
            ConfigureHttpClient();
        }

        /// <summary>
        /// Gets customer financial data from core banking system
        /// </summary>
        public async Task<CustomerFinancialData?> GetCustomerFinancialData(string accountNumber)
        {
            try
            {
                var endpoint = $"{_configuration["CoreSystem:BaseUrl"]}/api/customers/{accountNumber}/financial-data";
                
                var response = await _httpClient.GetAsync(endpoint);
                
                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var financialData = JsonSerializer.Deserialize<CustomerFinancialData>(jsonContent, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });

                    _logger.LogInformation("Retrieved financial data for account {AccountNumber}", accountNumber);
                    return financialData;
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    _logger.LogWarning("Financial data not found for account {AccountNumber}", accountNumber);
                    return null;
                }
                else
                {
                    _logger.LogError("Error retrieving financial data for account {AccountNumber}. Status: {StatusCode}", 
                        accountNumber, response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred while retrieving financial data for account {AccountNumber}", accountNumber);
                
                // Return mock data for development/testing
                if (_configuration.GetValue<bool>("CoreSystem:UseMockData"))
                {
                    return GetMockFinancialData(accountNumber);
                }
                
                return null;
            }
        }

        /// <summary>
        /// Reverts interest rate for a customer account
        /// </summary>
        public async Task<bool> RevertInterestRate(string accountNumber, string justification, string userId)
        {
            try
            {
                var endpoint = $"{_configuration["CoreSystem:BaseUrl"]}/api/accounts/{accountNumber}/revert-interest-rate";
                
                var requestData = new
                {
                    AccountNumber = accountNumber,
                    Justification = justification,
                    RequestedBy = userId,
                    RequestDate = DateTime.UtcNow
                };

                var jsonContent = JsonSerializer.Serialize(requestData, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                var content = new StringContent(jsonContent, System.Text.Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync(endpoint, content);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Interest rate reverted successfully for account {AccountNumber} by {UserId}", 
                        accountNumber, userId);
                    return true;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to revert interest rate for account {AccountNumber}. Status: {StatusCode}, Error: {Error}", 
                        accountNumber, response.StatusCode, errorContent);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred while reverting interest rate for account {AccountNumber}", accountNumber);
                
                // Return true for development/testing with mock data
                if (_configuration.GetValue<bool>("CoreSystem:UseMockData"))
                {
                    _logger.LogInformation("Mock: Interest rate reverted for account {AccountNumber}", accountNumber);
                    return true;
                }
                
                return false;
            }
        }

        /// <summary>
        /// Gets customer account information
        /// </summary>
        public async Task<CustomerAccountInfo?> GetCustomerAccountInfo(string accountNumber)
        {
            try
            {
                var endpoint = $"{_configuration["CoreSystem:BaseUrl"]}/api/customers/{accountNumber}/account-info";
                
                var response = await _httpClient.GetAsync(endpoint);
                
                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var accountInfo = JsonSerializer.Deserialize<CustomerAccountInfo>(jsonContent, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });

                    _logger.LogInformation("Retrieved account info for account {AccountNumber}", accountNumber);
                    return accountInfo;
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    _logger.LogWarning("Account info not found for account {AccountNumber}", accountNumber);
                    return null;
                }
                else
                {
                    _logger.LogError("Error retrieving account info for account {AccountNumber}. Status: {StatusCode}", 
                        accountNumber, response.StatusCode);
                    return null;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred while retrieving account info for account {AccountNumber}", accountNumber);
                
                // Return mock data for development/testing
                if (_configuration.GetValue<bool>("CoreSystem:UseMockData"))
                {
                    return GetMockAccountInfo(accountNumber);
                }
                
                return null;
            }
        }

        /// <summary>
        /// Validates if a customer account exists and is active
        /// </summary>
        public async Task<bool> ValidateCustomerAccount(string accountNumber)
        {
            try
            {
                var endpoint = $"{_configuration["CoreSystem:BaseUrl"]}/api/customers/{accountNumber}/validate";
                
                var response = await _httpClient.GetAsync(endpoint);
                
                if (response.IsSuccessStatusCode)
                {
                    var jsonContent = await response.Content.ReadAsStringAsync();
                    var validationResult = JsonSerializer.Deserialize<AccountValidationResult>(jsonContent, new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    });

                    _logger.LogInformation("Account validation for {AccountNumber}: {IsValid}", 
                        accountNumber, validationResult?.IsValid ?? false);
                    
                    return validationResult?.IsValid ?? false;
                }
                else
                {
                    _logger.LogWarning("Account validation failed for {AccountNumber}. Status: {StatusCode}", 
                        accountNumber, response.StatusCode);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred while validating account {AccountNumber}", accountNumber);
                
                // Return true for development/testing with mock data
                if (_configuration.GetValue<bool>("CoreSystem:UseMockData"))
                {
                    return !string.IsNullOrEmpty(accountNumber) && accountNumber.Length >= 10;
                }
                
                return false;
            }
        }

        private void ConfigureHttpClient()
        {
            var baseUrl = _configuration["CoreSystem:BaseUrl"];
            var apiKey = _configuration["CoreSystem:ApiKey"];
            var timeout = _configuration.GetValue<int>("CoreSystem:TimeoutSeconds", 30);

            if (!string.IsNullOrEmpty(baseUrl))
            {
                _httpClient.BaseAddress = new Uri(baseUrl);
            }

            _httpClient.Timeout = TimeSpan.FromSeconds(timeout);

            // Add authentication headers
            if (!string.IsNullOrEmpty(apiKey))
            {
                _httpClient.DefaultRequestHeaders.Add("X-API-Key", apiKey);
            }

            // Add standard headers
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "LoanCovenantAPI/1.0");
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
        }

        private static CustomerFinancialData GetMockFinancialData(string accountNumber)
        {
            // Generate mock data based on account number for consistency
            var hash = accountNumber.GetHashCode();
            var random = new Random(Math.Abs(hash));

            return new CustomerFinancialData
            {
                AccountNumber = accountNumber,
                DebtToEquityRatio = Math.Round((decimal)(random.NextDouble() * 2 + 0.5), 2),
                CurrentRatio = Math.Round((decimal)(random.NextDouble() * 3 + 1), 2),
                DebtServiceCoverageRatio = Math.Round((decimal)(random.NextDouble() * 2 + 0.8), 2),
                AnnualTurnover = Math.Round((decimal)(random.NextDouble() * 1000000 + 500000), 2),
                NetWorth = Math.Round((decimal)(random.NextDouble() * 5000000 + 1000000), 2),
                DataDate = DateTime.UtcNow.AddDays(-random.Next(1, 30))
            };
        }

        private static CustomerAccountInfo GetMockAccountInfo(string accountNumber)
        {
            return new CustomerAccountInfo
            {
                AccountNumber = accountNumber,
                CustomerName = $"Customer {accountNumber.Substring(Math.Max(0, accountNumber.Length - 4))}",
                AccountType = "Current Account",
                AccountStatus = "Active",
                BranchCode = "001",
                BranchName = "Head Office",
                AccountOpenDate = DateTime.UtcNow.AddYears(-2),
                RelationshipManager = "John Doe",
                RmEmail = "<EMAIL>"
            };
        }
    }

    /// <summary>
    /// Customer account information
    /// </summary>
    public class CustomerAccountInfo
    {
        public string AccountNumber { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string AccountType { get; set; } = string.Empty;
        public string AccountStatus { get; set; } = string.Empty;
        public string BranchCode { get; set; } = string.Empty;
        public string BranchName { get; set; } = string.Empty;
        public DateTime AccountOpenDate { get; set; }
        public string RelationshipManager { get; set; } = string.Empty;
        public string RmEmail { get; set; } = string.Empty;
    }

    /// <summary>
    /// Account validation result
    /// </summary>
    public class AccountValidationResult
    {
        public bool IsValid { get; set; }
        public string Message { get; set; } = string.Empty;
        public string AccountStatus { get; set; } = string.Empty;
    }
}
