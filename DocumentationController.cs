using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using System.IO;

namespace LoanCovenantAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DocumentationController : ControllerBase
    {
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<DocumentationController> _logger;

        public DocumentationController(IWebHostEnvironment environment, ILogger<DocumentationController> logger)
        {
            _environment = environment;
            _logger = logger;
        }

        /// <summary>
        /// Downloads the API documentation file
        /// </summary>
        /// <returns>The documentation file</returns>
        [HttpGet("download")]
        [ProducesResponseType(typeof(FileResult), 200)]
        [ProducesResponseType(404)]
        public async Task<IActionResult> DownloadDocumentation()
        {
            try
            {
                var filePath = Path.Combine(_environment.ContentRootPath, "docs", "LoanCovenantAPI-Documentation.docx");
                
                if (!System.IO.File.Exists(filePath))
                {
                    _logger.LogWarning("Documentation file not found at {FilePath}", filePath);
                    return NotFound("Documentation file not found.");
                }

                var provider = new FileExtensionContentTypeProvider();
                if (!provider.TryGetContentType(filePath, out var contentType))
                {
                    contentType = "application/octet-stream";
                }

                var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
                return File(fileBytes, contentType, "LoanCovenantAPI-Documentation.docx");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading documentation");
                return StatusCode(500, "An error occurred while downloading the documentation.");
            }
        }

        /// <summary>
        /// Gets documentation metadata
        /// </summary>
        /// <returns>Documentation information</returns>
        [HttpGet("info")]
        [ProducesResponseType(typeof(DocumentationInfo), 200)]
        public IActionResult GetDocumentationInfo()
        {
            try
            {
                var filePath = Path.Combine(_environment.ContentRootPath, "docs", "LoanCovenantAPI-Documentation.docx");
                
                if (!System.IO.File.Exists(filePath))
                {
                    return NotFound("Documentation file not found.");
                }

                var fileInfo = new FileInfo(filePath);
                var docInfo = new DocumentationInfo
                {
                    FileName = fileInfo.Name,
                    FileSize = fileInfo.Length,
                    LastModified = fileInfo.LastWriteTime,
                    Version = GetDocumentationVersion(),
                    Description = "Loan Covenant API Documentation"
                };

                return Ok(docInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting documentation info");
                return StatusCode(500, "An error occurred while retrieving documentation information.");
            }
        }

        private string GetDocumentationVersion()
        {
            // You can implement version tracking logic here
            // For example, read from a version file or use assembly version
            return "1.0.0";
        }
    }

    public class DocumentationInfo
    {
        public string FileName { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public DateTime LastModified { get; set; }
        public string Version { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }
}
