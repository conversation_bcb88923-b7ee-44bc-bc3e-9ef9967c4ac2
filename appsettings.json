{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=LoanCovenantDB;Trusted_Connection=true;MultipleActiveResultSets=true"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "CoreSystem": {"BaseUrl": "https://api.wemabank.com/core", "ApiKey": "your-api-key-here", "TimeoutSeconds": 30, "UseMockData": true}, "Email": {"SmtpHost": "smtp.office365.com", "SmtpPort": "587", "FromAddress": "<EMAIL>", "Username": "<EMAIL>", "Password": "your-email-password"}, "ActiveDirectory": {"Domain": "wemabank.com", "LdapPath": "LDAP://DC=wemabank,DC=com", "ServiceAccount": "your-service-account", "ServicePassword": "your-service-password"}, "WemaAnalytics": {"BaseUrl": "https://analytics.wemabank.com/api", "ApiKey": "your-analytics-api-key", "ReportEndpoint": "/reports/covenant-compliance"}, "Notifications": {"EnableEmailNotifications": true, "EnableSmsNotifications": false, "DefaultRecipients": ["<EMAIL>", "<EMAIL>"], "ComplianceAlertTemplate": "ComplianceAlert", "PeriodicReportTemplate": "PeriodicReport"}, "Security": {"RequireAuthentication": true, "EnableAuditLogging": true, "SessionTimeoutMinutes": 30, "MaxLoginAttempts": 3}, "Features": {"EnableAutomaticComplianceChecks": true, "EnableConcessionManagement": true, "EnableInterestRateReversion": true, "EnablePeriodicReports": true, "ComplianceCheckIntervalMinutes": 60}}