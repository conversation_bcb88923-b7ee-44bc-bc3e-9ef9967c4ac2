using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using System.Text;

namespace LoanCovenantAPI.Services
{
    public interface IDocumentationService
    {
        Task<string> ExtractTextFromWordDocumentAsync(string filePath);
        Task<DocumentationContent> ParseDocumentationAsync(string filePath);
        Task<bool> ValidateDocumentationAsync(string filePath);
    }

    public class DocumentationService : IDocumentationService
    {
        private readonly ILogger<DocumentationService> _logger;

        public DocumentationService(ILogger<DocumentationService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Extracts plain text from a Word document
        /// </summary>
        /// <param name="filePath">Path to the Word document</param>
        /// <returns>Extracted text content</returns>
        public async Task<string> ExtractTextFromWordDocumentAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"Documentation file not found: {filePath}");
                }

                using var document = WordprocessingDocument.Open(filePath, false);
                var body = document.MainDocumentPart?.Document?.Body;
                
                if (body == null)
                {
                    return string.Empty;
                }

                var text = new StringBuilder();
                foreach (var paragraph in body.Elements<Paragraph>())
                {
                    text.AppendLine(paragraph.InnerText);
                }

                return text.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting text from Word document: {FilePath}", filePath);
                throw;
            }
        }

        /// <summary>
        /// Parses the documentation and extracts structured content
        /// </summary>
        /// <param name="filePath">Path to the Word document</param>
        /// <returns>Parsed documentation content</returns>
        public async Task<DocumentationContent> ParseDocumentationAsync(string filePath)
        {
            try
            {
                var content = new DocumentationContent();
                
                using var document = WordprocessingDocument.Open(filePath, false);
                var body = document.MainDocumentPart?.Document?.Body;
                
                if (body == null)
                {
                    return content;
                }

                var sections = new List<DocumentationSection>();
                var currentSection = new DocumentationSection();
                
                foreach (var element in body.Elements())
                {
                    if (element is Paragraph paragraph)
                    {
                        var text = paragraph.InnerText.Trim();
                        if (string.IsNullOrEmpty(text)) continue;

                        // Check if this is a heading (you may need to adjust this logic)
                        var isHeading = IsHeading(paragraph);
                        
                        if (isHeading && !string.IsNullOrEmpty(currentSection.Title))
                        {
                            sections.Add(currentSection);
                            currentSection = new DocumentationSection();
                        }

                        if (isHeading)
                        {
                            currentSection.Title = text;
                        }
                        else
                        {
                            currentSection.Content += text + Environment.NewLine;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(currentSection.Title))
                {
                    sections.Add(currentSection);
                }

                content.Sections = sections;
                content.LastUpdated = File.GetLastWriteTime(filePath);
                
                return content;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing documentation: {FilePath}", filePath);
                throw;
            }
        }

        /// <summary>
        /// Validates that the documentation file is accessible and properly formatted
        /// </summary>
        /// <param name="filePath">Path to the Word document</param>
        /// <returns>True if valid, false otherwise</returns>
        public async Task<bool> ValidateDocumentationAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    _logger.LogWarning("Documentation file does not exist: {FilePath}", filePath);
                    return false;
                }

                // Check file extension
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                if (extension != ".docx" && extension != ".doc")
                {
                    _logger.LogWarning("Invalid documentation file format: {Extension}", extension);
                    return false;
                }

                // Try to open the document
                using var document = WordprocessingDocument.Open(filePath, false);
                var body = document.MainDocumentPart?.Document?.Body;
                
                return body != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating documentation: {FilePath}", filePath);
                return false;
            }
        }

        private static bool IsHeading(Paragraph paragraph)
        {
            // Check if paragraph has heading style
            var paragraphProperties = paragraph.ParagraphProperties;
            if (paragraphProperties?.ParagraphStyleId?.Val?.Value != null)
            {
                var styleId = paragraphProperties.ParagraphStyleId.Val.Value;
                return styleId.StartsWith("Heading", StringComparison.OrdinalIgnoreCase);
            }

            // Alternative: check for bold formatting or other heading indicators
            var runs = paragraph.Elements<Run>();
            return runs.Any(run => 
                run.RunProperties?.Bold?.Val?.Value == true ||
                run.RunProperties?.FontSize?.Val?.Value > 24);
        }
    }

    public class DocumentationContent
    {
        public List<DocumentationSection> Sections { get; set; } = new();
        public DateTime LastUpdated { get; set; }
        public string Version { get; set; } = "1.0.0";
    }

    public class DocumentationSection
    {
        public string Title { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public int Level { get; set; } = 1;
    }
}
