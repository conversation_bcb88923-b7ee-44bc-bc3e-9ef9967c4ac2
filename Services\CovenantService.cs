using LoanCovenantAPI.Data;
using LoanCovenantAPI.Models;
using Microsoft.EntityFrameworkCore;

namespace LoanCovenantAPI.Services
{
    /// <summary>
    /// Service interface for covenant operations
    /// </summary>
    public interface ICovenantService
    {
        Task<bool> CanUserCreateCovenants(string userId);
        Task<bool> CanUserModifyCovenants(string userId);
        Task<bool> CanUserManageConcessions(string userId);
        Task<CovenantComplianceHistory?> PerformComplianceCheck(int covenantId, string checkedBy);
        Task ScheduleComplianceCheck(int covenantId);
        Task<List<LoanCovenant>> GetCovenantsForReview();
        Task<bool> RevertInterestRate(int covenantId, string userId, string justification);
    }

    /// <summary>
    /// Service for covenant business logic
    /// </summary>
    public class CovenantService : ICovenantService
    {
        private readonly LoanCovenantDbContext _context;
        private readonly INotificationService _notificationService;
        private readonly ICoreSystemIntegrationService _coreSystemService;
        private readonly ILogger<CovenantService> _logger;

        public CovenantService(
            LoanCovenantDbContext context,
            INotificationService notificationService,
            ICoreSystemIntegrationService coreSystemService,
            ILogger<CovenantService> logger)
        {
            _context = context;
            _notificationService = notificationService;
            _coreSystemService = coreSystemService;
            _logger = logger;
        }

        /// <summary>
        /// Checks if a user can create covenants
        /// </summary>
        public async Task<bool> CanUserCreateCovenants(string userId)
        {
            var userRole = await GetUserRole(userId);
            return userRole?.CanCreateCovenants ?? false;
        }

        /// <summary>
        /// Checks if a user can modify covenants
        /// </summary>
        public async Task<bool> CanUserModifyCovenants(string userId)
        {
            var userRole = await GetUserRole(userId);
            return userRole?.CanModifyCovenants ?? false;
        }

        /// <summary>
        /// Checks if a user can manage concessions
        /// </summary>
        public async Task<bool> CanUserManageConcessions(string userId)
        {
            var userRole = await GetUserRole(userId);
            return userRole?.CanManageConcessions ?? false;
        }

        /// <summary>
        /// Performs a compliance check for a covenant
        /// </summary>
        public async Task<CovenantComplianceHistory?> PerformComplianceCheck(int covenantId, string checkedBy)
        {
            try
            {
                var covenant = await _context.LoanCovenants.FindAsync(covenantId);
                if (covenant == null)
                {
                    return null;
                }

                // Get current financial data from core system
                var currentData = await _coreSystemService.GetCustomerFinancialData(covenant.CustomerAccountNumber);
                
                var complianceHistory = new CovenantComplianceHistory
                {
                    CovenantId = covenantId,
                    CheckDate = DateTime.UtcNow,
                    CheckedBy = checkedBy,
                    CreatedDate = DateTime.UtcNow,
                    IsSystemGenerated = false,
                    DataSource = "Manual Check"
                };

                // Determine compliance based on covenant type
                if (covenant.CovenantType == CovenantType.Financial && covenant.TargetValue.HasValue)
                {
                    var actualValue = GetActualValueForCovenant(covenant, currentData);
                    complianceHistory.ActualValue = actualValue;
                    complianceHistory.RequiredValue = covenant.TargetValue;

                    if (actualValue.HasValue && covenant.TargetValue.HasValue)
                    {
                        complianceHistory.VarianceAmount = actualValue.Value - covenant.TargetValue.Value;
                        complianceHistory.VariancePercentage = covenant.TargetValue.Value != 0 
                            ? (complianceHistory.VarianceAmount / covenant.TargetValue.Value) * 100 
                            : 0;

                        // Check compliance based on operator
                        complianceHistory.ComplianceStatus = EvaluateCompliance(
                            actualValue.Value, 
                            covenant.TargetValue.Value, 
                            covenant.ComparisonOperator ?? ">=");
                    }
                    else
                    {
                        complianceHistory.ComplianceStatus = ComplianceStatus.PendingReview;
                        complianceHistory.ComplianceNotes = "Unable to retrieve current financial data";
                    }
                }
                else
                {
                    // For non-financial covenants, mark as pending review
                    complianceHistory.ComplianceStatus = ComplianceStatus.PendingReview;
                    complianceHistory.ComplianceNotes = "Manual review required for non-financial covenant";
                }

                // Update covenant compliance status
                covenant.ComplianceStatus = complianceHistory.ComplianceStatus;
                covenant.LastComplianceCheck = DateTime.UtcNow;
                covenant.CurrentValue = complianceHistory.ActualValue;

                if (complianceHistory.ComplianceStatus == ComplianceStatus.Compliant)
                {
                    covenant.LastComplianceDate = DateTime.UtcNow;
                }

                _context.CovenantComplianceHistories.Add(complianceHistory);
                await _context.SaveChangesAsync();

                // Send notifications if non-compliant
                if (complianceHistory.ComplianceStatus == ComplianceStatus.NonCompliant)
                {
                    await _notificationService.SendComplianceAlert(covenant, complianceHistory);
                }

                _logger.LogInformation("Compliance check completed for covenant {CovenantId}. Status: {Status}", 
                    covenantId, complianceHistory.ComplianceStatus);

                return complianceHistory;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing compliance check for covenant {CovenantId}", covenantId);
                throw;
            }
        }

        /// <summary>
        /// Schedules a compliance check for a covenant
        /// </summary>
        public async Task ScheduleComplianceCheck(int covenantId)
        {
            var covenant = await _context.LoanCovenants.FindAsync(covenantId);
            if (covenant == null) return;

            // Calculate next review date based on monitoring frequency
            var nextReviewDate = CalculateNextReviewDate(covenant.MonitoringFrequency);
            covenant.NextReviewDate = nextReviewDate;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Compliance check scheduled for covenant {CovenantId} on {NextReviewDate}", 
                covenantId, nextReviewDate);
        }

        /// <summary>
        /// Gets covenants that are due for review
        /// </summary>
        public async Task<List<LoanCovenant>> GetCovenantsForReview()
        {
            var today = DateTime.UtcNow.Date;
            
            return await _context.LoanCovenants
                .Where(c => c.Status == CovenantStatus.Active && 
                           c.NextReviewDate.Date <= today)
                .Include(c => c.ComplianceHistory.OrderByDescending(h => h.CheckDate).Take(1))
                .ToListAsync();
        }

        /// <summary>
        /// Reverts interest rate for a covenant with concession
        /// </summary>
        public async Task<bool> RevertInterestRate(int covenantId, string userId, string justification)
        {
            try
            {
                var covenant = await _context.LoanCovenants.FindAsync(covenantId);
                if (covenant == null || !covenant.HasConcession)
                {
                    return false;
                }

                // Check user permissions
                if (!await CanUserManageConcessions(userId))
                {
                    throw new UnauthorizedAccessException("User does not have permission to revert interest rates");
                }

                // Call core system to revert interest rate
                var success = await _coreSystemService.RevertInterestRate(
                    covenant.CustomerAccountNumber, 
                    justification, 
                    userId);

                if (success)
                {
                    // Update concession status
                    covenant.IsConcessionActive = false;
                    covenant.ModifiedBy = userId;
                    covenant.ModifiedDate = DateTime.UtcNow;

                    // Add compliance history entry
                    var complianceHistory = new CovenantComplianceHistory
                    {
                        CovenantId = covenantId,
                        CheckDate = DateTime.UtcNow,
                        ComplianceStatus = ComplianceStatus.Compliant,
                        ComplianceNotes = $"Interest rate reverted. Justification: {justification}",
                        CheckedBy = userId,
                        CreatedDate = DateTime.UtcNow,
                        IsSystemGenerated = false,
                        DataSource = "Interest Rate Reversion"
                    };

                    _context.CovenantComplianceHistories.Add(complianceHistory);
                    await _context.SaveChangesAsync();

                    // Send notification
                    await _notificationService.SendConcessionRevertedNotification(covenant, justification);

                    _logger.LogInformation("Interest rate reverted for covenant {CovenantId} by {UserId}", 
                        covenantId, userId);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reverting interest rate for covenant {CovenantId}", covenantId);
                throw;
            }
        }

        private async Task<UserRole?> GetUserRole(string userId)
        {
            var assignment = await _context.UserRoleAssignments
                .Include(a => a.UserRole)
                .FirstOrDefaultAsync(a => a.UserId == userId && a.IsActive);

            return assignment?.UserRole;
        }

        private static decimal? GetActualValueForCovenant(LoanCovenant covenant, CustomerFinancialData? data)
        {
            if (data == null) return null;

            // Map covenant measurement to actual financial data
            return covenant.MeasurementUnit?.ToLower() switch
            {
                "debt_to_equity" => data.DebtToEquityRatio,
                "current_ratio" => data.CurrentRatio,
                "debt_service_coverage" => data.DebtServiceCoverageRatio,
                "turnover" => data.AnnualTurnover,
                "net_worth" => data.NetWorth,
                _ => null
            };
        }

        private static ComplianceStatus EvaluateCompliance(decimal actualValue, decimal targetValue, string comparisonOperator)
        {
            return comparisonOperator switch
            {
                ">=" => actualValue >= targetValue ? ComplianceStatus.Compliant : ComplianceStatus.NonCompliant,
                "<=" => actualValue <= targetValue ? ComplianceStatus.Compliant : ComplianceStatus.NonCompliant,
                ">" => actualValue > targetValue ? ComplianceStatus.Compliant : ComplianceStatus.NonCompliant,
                "<" => actualValue < targetValue ? ComplianceStatus.Compliant : ComplianceStatus.NonCompliant,
                "=" => actualValue == targetValue ? ComplianceStatus.Compliant : ComplianceStatus.NonCompliant,
                _ => ComplianceStatus.PendingReview
            };
        }

        private static DateTime CalculateNextReviewDate(ComplianceFrequency frequency)
        {
            var today = DateTime.UtcNow;
            
            return frequency switch
            {
                ComplianceFrequency.Daily => today.AddDays(1),
                ComplianceFrequency.Weekly => today.AddDays(7),
                ComplianceFrequency.Monthly => today.AddMonths(1),
                ComplianceFrequency.Quarterly => today.AddMonths(3),
                ComplianceFrequency.SemiAnnually => today.AddMonths(6),
                ComplianceFrequency.Annually => today.AddYears(1),
                _ => today.AddMonths(1)
            };
        }
    }

    /// <summary>
    /// Customer financial data from core system
    /// </summary>
    public class CustomerFinancialData
    {
        public string AccountNumber { get; set; } = string.Empty;
        public decimal? DebtToEquityRatio { get; set; }
        public decimal? CurrentRatio { get; set; }
        public decimal? DebtServiceCoverageRatio { get; set; }
        public decimal? AnnualTurnover { get; set; }
        public decimal? NetWorth { get; set; }
        public DateTime DataDate { get; set; }
    }
}
